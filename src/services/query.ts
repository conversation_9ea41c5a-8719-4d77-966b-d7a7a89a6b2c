import axios from 'axios'
// 将模拟数据的导入改为从新的mock系统导入
import { mockQueries } from '@/mock/data/query'
import type {
  Query,
  QueryParameter,
  QueryResult,
  QueryDisplayConfig,
  ChartConfig,
  QueryExecutionPlan,
  QueryVisualization,
  QueryServiceStatus,
  QueryFavorite,
  PageResponse,
  NaturalLanguageQueryParams
} from '@/types/query'
import type { ExecutionHistory } from '@/types/executionHistory'
import type { PaginationInfo } from '@/types/common'
import { getQueryApiUrl } from '@/services/apiUtils'
import instance from "@/utils/axios";

// 自定义类型定义
export interface QueryExecutionResult {
  id?: string;  // 添加id属性
  columns: Array<{
    field: string;
    label: string;
    type: string;
  }>;
  rows: any[];
  pagination?: {
    total: number;
    page: number;
    pageSize: number;
  };
}

/**
 * 获取API基础URL
 * 确保所有请求都使用相同的基础URL，避免重复的/api
 */
export function getApiBaseUrl(): string {
  // 使用环境变量判断是否为Mock模式
  const isMockMode = import.meta.env.VITE_USE_MOCK_API === 'true';
  const isDev = import.meta.env.DEV;

  if (isMockMode) {
    // 对于Mock模式，返回空字符串，让请求使用相对路径
    // 这样会自动使用当前域名作为基础URL
    console.log('[API] Mock模式，使用空基础URL');
    return '';
  }

  // 开发环境使用配置的API地址
  if (isDev) {
    // 使用实际的后端API基础URL
    let baseUrl = import.meta.env.VITE_API_BASE_URL || '';

    // 检查是否末尾有斜杠，确保格式一致性
    if (baseUrl && baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    console.log('[API] 开发环境，使用基础URL:', baseUrl || '(当前域名)');
    return baseUrl;
  }

  // 生产环境使用当前域名
  console.log('[API] 生产环境，使用当前域名作为基础URL');
  return '';
}

// 查询参数类型
export interface QueryParams {
  queryType?: string;
  status?: string;
  serviceStatus?: string;
  dataSourceId?: string;
  search?: string;
  searchTerm?: string;
  sortBy?: string;
  sortDir?: string;
  includeDrafts?: boolean;
  page?: number;
  size?: number;
}

// 添加状态标准化辅助方法
const standardizeStatus = (status: string | undefined): string => {
  if (!status) return '';
  return status.toUpperCase();
};

// 添加完整的查询对象标准化方法
const standardizeQuery = (query: any): any => {
  if (!query) return query;

  // 创建一个新对象，避免修改原始对象
  const standardized = { ...query };

  // 标准化状态字段
  if (standardized.serviceStatus) {
    standardized.serviceStatus = standardizeStatus(standardized.serviceStatus);
  }
  if (standardized.status) {
    standardized.status = standardizeStatus(standardized.status);
  }

  return standardized;
};

// 辅助函数：统一处理HTTP响应
const handleApiResponse = (response: any) => {
  console.log('handleApiResponse - 输入:', response);

  // 如果response本身不是我们需要的数据，而是axios的响应对象
  if (response && response.data !== undefined) {
    // 使用response.data作为实际数据
    console.log('handleApiResponse - 返回response.data');
    return response.data;
  }

  console.log('handleApiResponse - 直接返回response');
  return response;
};

// 查询服务实现
const queryService = {
  /**
   * 获取查询列表
   * @param params 查询参数
   * @returns 查询列表和分页信息
   */
  async getQueries(params: any = {}): Promise<any> {
    try {
      const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';
      console.log('获取查询列表, 使用Mock:', USE_MOCK, '参数:', params);

      // 标准化请求参数，统一使用searchTerm
      const queryParams = {
        page: params.page || 1,
        size: params.size || 10,
        // 只使用searchTerm参数，后端API支持searchTerm
        searchTerm: params.searchTerm || '',
        serviceStatus: params.serviceStatus,
        queryType: params.queryType,
        sortBy: params.sortBy,
        sortDir: params.sortDir,
        includeDrafts: params.includeDrafts,
        dataSourceId: params.dataSourceId // 添加 dataSourceId 参数
      };

      // 使用getQueryApiUrl函数获取正确的API路径
      const apiUrl = getQueryApiUrl('list');
      console.log('使用查询API URL:', apiUrl);

      // 发送API请求
      const response = await instance.get<any>(apiUrl, { params: queryParams });
      console.log('查询列表API响应:', response);

      // 处理响应：标准化状态字段
      if (response && response.data && response.data.success && response.data.data && response.data.data.items) {
        // 标准化每个查询对象的状态字段
        response.data.data.items = response.data.data.items.map(standardizeQuery);
      }

      return response;
    } catch (error) {
      console.error('获取查询列表失败:', error);
      // 不再静默处理错误，而是抛出错误让上层处理
      throw error;
    }
  },

  /**
   * 获取查询详情
   * @param id 查询ID
   * @returns 查询详情
   */
  async getQueryById(id: string): Promise<Query> {
    try {
      const apiUrl = getQueryApiUrl('detail', { id });
      console.log('获取查询详情URL:', apiUrl);

      const response = await instance.get<any>(apiUrl);

      // 处理响应数据
      if (response && response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      throw new Error('获取查询详情失败：无效的响应格式');
    } catch (error) {
      console.error(`获取查询失败 [${id}]:`, error);
      throw error;
    }
  },

  /**
   * 执行SQL查询
   * @param params 查询参数，包含数据源ID、SQL和参数
   * @returns 查询结果
   */
  async executeQuery(params: {
    dataSourceId: string;
    sql: string;
    queryType?: string;
    parameters?: Record<string, any> | any[];
    parameterMode?: 'named' | 'ordered';
    page?: number;
    size?: number;
  }): Promise<any> {
    // 解构参数，方便使用
    const { dataSourceId, sql, queryType, parameters, parameterMode = 'named', page, size } = params;

    // 校验数据源ID
    if (!dataSourceId) {
      throw new Error('请选择数据源');
    }

    // 校验SQL不能为空
    if (!sql || sql.trim() === '') {
      console.error('SQL查询不能为空');
      throw new Error('SQL查询不能为空，请在编辑器中输入查询语句');
    }

    // 详细日志记录
    console.log(`执行查询，SQL长度: ${sql.length}, SQL内容: ${sql.substring(0, 100)}${sql.length > 100 ? '...' : ''}`);

    // 定义mock数据
    const mockData = {
      columns: [
        { field: 'id', label: 'ID', type: 'string' },
        { field: 'name', label: '名称', type: 'string' }
      ],
      rows: Array.from({ length: 5 }, (_, i) => ({
        id: `record-${i+1}`,
        name: `示例数据 ${i+1}`
      }))
    };

    // 检查是否使用模拟API
    if (import.meta.env.VITE_USE_MOCK_API === 'true') {
      console.log('使用模拟API执行查询');
      await new Promise(resolve => setTimeout(resolve, 500));
      return mockData;
    }

    try {
      // 使用API工具函数获取正确的API端点
      const apiUrl = getQueryApiUrl('execute-sql');
      console.log('查询API URL:', apiUrl);

      // 处理参数对象 - 根据不同的参数模式
      let processedParams: any;

      if (parameterMode === 'ordered' && Array.isArray(parameters)) {
        // 有序参数模式 - 参数是一个数组，对应SQL中的?占位符顺序
        processedParams = parameters;
        console.log('使用有序参数模式，参数数组长度:', processedParams.length);
      } else {
        // 命名参数模式 (默认) - 参数是一个对象，使用名称映射
        processedParams = parameters || {};
        console.log('使用命名参数模式');
      }

      // 检查SQL中的参数占位符格式
      let sqlToExecute = sql;
      
      // 尝试预处理SQL中的参数占位符，支持searchTerm等常见参数名
      if (processedParams && typeof processedParams === 'object' && !Array.isArray(processedParams)) {
        // 检查是否存在常见的LIKE参数模式，比如${keyword}
        const paramMatches = sql.match(/\${([^}]+)}/g);
        
        if (paramMatches && paramMatches.length > 0) {
          console.log('SQL中检测到参数占位符:', paramMatches);
          
          // 遍历参数匹配
          for (const match of paramMatches) {
            // 提取参数名称 ${param} -> param
            const paramName = match.substring(2, match.length - 1);
            console.log(`检查参数: ${paramName}`);
            
            // 处理特殊参数名映射
            const mappedParamName = 
              paramName === 'keyword' && processedParams.searchTerm ? 'searchTerm' : paramName;
            
            // 如果参数对象中有对应值，直接替换
            if (mappedParamName in processedParams) {
              console.log(`替换参数: ${paramName} -> ${mappedParamName} = ${processedParams[mappedParamName]}`);
              // 保留值中的单引号，将参数替换为实际值
              sqlToExecute = sqlToExecute.replace(
                match, 
                String(processedParams[mappedParamName]).replace(/'/g, "''")
              );
            } else {
              console.log(`未找到参数: ${paramName}`);
            }
          }
          console.log('参数替换后的SQL:', sqlToExecute.substring(0, 100) + (sqlToExecute.length > 100 ? '...' : ''));
        }
      }

      // 核心重构：构建请求数据，确保使用正确的参数名，以确保后端API可以识别
      const requestData: any = {
        dataSourceId,
        sql: sqlToExecute,  // 使用处理后的SQL
        queryType: queryType || 'SQL',
        parameters: processedParams
      };

      // 添加分页参数
      if (page !== undefined) {
        requestData.page = page;
      }
      if (size !== undefined) {
        requestData.size = size;
      }

      // 如果是有序参数模式，添加参数模式标志
      if (parameterMode === 'ordered') {
        requestData.parameterMode = parameterMode;
      }

      // 更加详细的请求数据日志，便于调试
      console.log('⭐ 发送给API的请求数据:', JSON.stringify({
        ...requestData,
        sql: requestData.sql.substring(0, 100) + (requestData.sql.length > 100 ? '...' : '')
      }));

      // 特别检查分页参数
      console.log('🔍 分页参数检查:', {
        page: requestData.page,
        size: requestData.size,
        '是否包含分页参数': !!(requestData.page !== undefined && requestData.size !== undefined)
      });
      console.log('⭐ 请求参数:', typeof processedParams === 'object' ?
        (Array.isArray(processedParams) ?
          `[数组，长度=${processedParams.length}]` :
          JSON.stringify(processedParams)
        ) : '无参数');
      console.log('⭐ 请求URL:', apiUrl);

      // 使用http模块发送请求，确保包含认证信息
      const response = await instance.post(apiUrl, requestData);

      // 响应详细日志
      console.log('查询执行响应:', response.data);

      // 返回响应数据
      return response.data;
    } catch (error: any) {
      // 详细错误记录
      console.error('执行查询失败:', error);
      console.error('错误详情:', error.response?.data || error.message);

      // 抛出友好的错误信息
      throw new Error(error.response?.data?.message || error.message || '执行查询失败');
    }
  },

  /**
   * 执行查询版本
   * @param queryId 查询ID
   * @param versionId 版本ID
   * @param params 额外参数
   * @returns 查询执行结果
   */
  async executeQueryVersion(
    queryId: string,
    versionId: string,
    params: {
      parameters?: Record<string, any>;
    } = {}
  ): Promise<QueryExecutionResult> {
    try {
      console.log(`执行查询版本, 查询ID: ${queryId}, 版本ID: ${versionId}`);

      // 使用Mock数据模式
      if (import.meta.env.VITE_USE_MOCK_API === 'true') {
        console.log('使用Mock数据执行查询版本');

        // 创建一个模拟响应
        return {
          columns: [
            { field: 'id', label: 'ID', type: 'string' },
            { field: 'name', label: '名称', type: 'string' },
            { field: 'created_at', label: '创建时间', type: 'date' }
          ],
          rows: Array.from({ length: 5 }, (_, i) => ({
            id: `record-${i+1}`,
            name: `示例数据 ${i+1}`,
            created_at: new Date().toISOString()
          }))
        };
      }

      // 构造API路径
      const apiUrl = getQueryApiUrl('execute-version', { id: queryId, versionId });
      console.log('执行查询版本URL:', apiUrl);

      // 构造请求参数
      const requestData = {
        parameters: params.parameters || {},
      };

      // 如果传入了versionId，则添加到请求参数中
      // 否则让后端根据queryId决定使用哪个版本
      if (versionId) {
        // @ts-ignore
        requestData.version_id = versionId;
      }

      // 发送API请求
      const response = await instance.post<any>(apiUrl, requestData);

      console.log('查询版本执行结果:', response);

      // 添加更详细的日志输出
      console.log('查询版本执行结果类型:', typeof response);
      console.log('查询版本执行结果是否有data属性:', response && response.data !== undefined);
      if (response && response.data) {
        console.log('response.data类型:', typeof response.data);
        console.log('response.data是否有success属性:', response.data.success !== undefined);
        console.log('response.data是否有data属性:', response.data.data !== undefined);
        if (response.data.data) {
          console.log('response.data.data类型:', typeof response.data.data);
          console.log('response.data.data是否有rows属性:', response.data.data.rows !== undefined);
          console.log('response.data.data是否有columns属性:', response.data.data.columns !== undefined);
          if (response.data.data.rows) {
            console.log('response.data.data.rows类型:', typeof response.data.data.rows);
            console.log('response.data.data.rows是否为数组:', Array.isArray(response.data.data.rows));
            console.log('response.data.data.rows长度:', Array.isArray(response.data.data.rows) ? response.data.data.rows.length : '非数组');
            console.log('response.data.data.rows的前3个元素:', Array.isArray(response.data.data.rows) ? response.data.data.rows.slice(0, 3) : response.data.data.rows);
          }
        }
      }

      // 处理响应数据
      const responseData = handleApiResponse(response);

      // 打印处理后的响应数据
      console.log('处理后的响应数据:', responseData);
      console.log('处理后的响应数据类型:', typeof responseData);
      console.log('处理后的响应数据是否有success属性:', responseData && responseData.success !== undefined);
      console.log('处理后的响应数据是否有data属性:', responseData && responseData.data !== undefined);
      if (responseData && responseData.data) {
        console.log('responseData.data类型:', typeof responseData.data);
        console.log('responseData.data是否有rows属性:', responseData.data.rows !== undefined);
        console.log('responseData.data是否有columns属性:', responseData.data.columns !== undefined);
        if (responseData.data.rows) {
          console.log('responseData.data.rows类型:', typeof responseData.data.rows);
          console.log('responseData.data.rows是否为数组:', Array.isArray(responseData.data.rows));
          console.log('responseData.data.rows长度:', Array.isArray(responseData.data.rows) ? responseData.data.rows.length : '非数组');
        }
      }

      // 处理标准化的API响应格式
      if (responseData) {
        // 情况1: 标准响应格式 { success, data }
        if (responseData.success && responseData.data) {
          // 检查不同的响应结构
          if (responseData.data.columns && responseData.data.rows) {
            return {
              columns: Array.isArray(responseData.data.columns)
                ? responseData.data.columns.map((col: any) => typeof col === 'string'
                  ? { field: col, label: col, type: 'string' }
                  : { field: col.field || col.name, label: col.label || col.name, type: col.type || 'string' })
                : [],
              rows: responseData.data.rows || [],
              pagination: responseData.data.pagination || undefined
            };
          } else if (responseData.data.data && responseData.data.data.fields && responseData.data.data.rows) {
            return {
              columns: responseData.data.data.fields.map((field: any) => ({
                field: field.name || field,
                label: field.label || field.name || field,
                type: field.type || 'string'
              })),
              rows: responseData.data.data.rows,
              pagination: {
                total: responseData.data.data.totalCount || responseData.data.data.rows.length,
                page: responseData.data.data.page || 1,
                pageSize: responseData.data.data.pageSize || responseData.data.data.rows.length
              }
            };
          }
        }
        // 情况2: 查询执行记录格式 (包含id, queryId, status等)
        else if (responseData.id && responseData.queryId && responseData.status === 'success' && responseData.rows) {
          // 直接包含rows和columns的格式
          return {
            columns: Array.isArray(responseData.columns)
              ? responseData.columns.map((col: any) => typeof col === 'string'
                ? { field: col, label: col, type: 'string' }
                : { field: col.field || col.name, label: col.label || col.name, type: col.type || 'string' })
              : [],
            rows: responseData.rows || [],
            pagination: responseData.pagination || undefined
          };
        }
        // 情况3: 其他可能的格式，尝试从各种可能的位置提取数据
        else if (responseData.rows || responseData.data?.rows || responseData.result?.rows) {
          const rows = responseData.rows || responseData.data?.rows || responseData.result?.rows || [];
          const columns = responseData.columns || responseData.data?.columns || responseData.result?.columns || [];

          return {
            columns: Array.isArray(columns)
              ? columns.map((col: any) => typeof col === 'string'
                ? { field: col, label: col, type: 'string' }
                : { field: col.field || col.name, label: col.label || col.name, type: col.type || 'string' })
              : [],
            rows: rows,
            pagination: responseData.pagination || responseData.data?.pagination || undefined
          };
        }
      }

      // 兜底：在收到有效但格式不匹配的响应时，创建一个简单的result以避免错误
      console.warn('查询版本执行返回了不标准的格式，尝试生成默认结果:', responseData);

      if (responseData) {
        // 如果是简单的对象格式，创建一个单行结果集，将对象的所有属性作为列
        const keys = Object.keys(responseData).filter(key =>
          typeof responseData[key] !== 'object' ||
          responseData[key] === null ||
          (responseData[key] instanceof Array && !responseData[key].some(item => typeof item === 'object'))
        );

        if (keys.length > 0) {
          return {
            columns: keys.map(key => ({ field: key, label: key, type: 'string' })),
            rows: [responseData]
          };
        }
      }

      // 返回空结果
      console.error('查询版本执行返回了无效的格式，无法转换为有效数据:', responseData);
      return {
        columns: [],
        rows: []
      };
    } catch (error) {
      console.error(`执行查询版本失败, 查询ID: ${queryId}, 版本ID: ${versionId}:`, error);
      throw error;
    }
  },

  /**
   * 执行自然语言查询
   * @param queryId 查询ID
   * @param versionId 版本ID
   * @param params 额外参数
   * @returns 查询执行结果
   */
  async executeNaturalLanguageQuery(params: {
    dataSourceId: string,
    question: string,
    parameters?: Record<string, any>
  }): Promise<QueryResult> {
    try {
      console.log('执行自然语言查询, 参数:', params);

      // 使用Mock数据模式
      if (import.meta.env.VITE_USE_MOCK_API === 'true') {
        console.log('使用Mock数据执行自然语言查询');

        // 延迟模拟
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 返回Mock数据
        return {
          id: 'nlq-mock-' + Date.now(),
          columns: [
            { field: 'id', label: 'ID', type: 'string' },
            { field: 'name', label: '名称', type: 'string' },
            { field: 'created_at', label: '创建时间', type: 'date' }
          ],
          rows: Array.from({ length: 10 }, (_, i) => ({
            id: `record-${i+1}`,
            name: `自然语言查询结果 ${i+1}`,
            created_at: new Date().toISOString()
          })),
          sql: `-- 由AI生成的SQL查询\nSELECT * FROM table WHERE description LIKE '%${params.question}%'`,
          status: 'COMPLETED',
          createdAt: new Date().toISOString(),
          finishedAt: new Date().toISOString(),
          rowCount: 10,
          executionTime: 800
        };
      }

      // 构造请求参数
      const requestData = {
        question: params.question,
        parameters: params.parameters || {},
      };

      // 构造API路径
      const apiUrl = getQueryApiUrl('nlq-execute');
      console.log('执行自然语言查询URL:', apiUrl);

      // 发送API请求
      const response = await instance.post<any>(
        `${apiUrl}?dataSourceId=${params.dataSourceId}`,
        requestData
      );

      // 处理响应数据
      console.log('自然语言查询API响应:', response);
      const responseData = handleApiResponse(response);

      if (responseData && responseData.success && responseData.data) {
        // 转换API响应为QueryResult格式
        return {
          id: responseData.data.id || ('nlq-' + Date.now()),
          columns: responseData.data.fields ?
            responseData.data.fields.map((field: any) => ({
              field: typeof field === 'string' ? field : field.name || field.field,
              label: typeof field === 'string' ? field : field.label || field.name || field.field,
              type: typeof field === 'string' ? 'string' : field.type || 'string'
            })) : [],
          rows: responseData.data.rows || [],
          sql: responseData.data.sql || '',
          status: responseData.data.status || 'COMPLETED',
          createdAt: responseData.data.createdAt || new Date().toISOString(),
          finishedAt: responseData.data.finishedAt || new Date().toISOString(),
          rowCount: responseData.data.rowCount || (responseData.data.rows ? responseData.data.rows.length : 0),
          executionTime: responseData.data.executionTime || 0
        };
      }

      // 返回空结果
      console.error('自然语言查询执行返回了无效的格式:', responseData);
      return {
        id: 'error-' + Date.now(),
        columns: [],
        rows: [],
        sql: '',
        status: 'FAILED',
        createdAt: new Date().toISOString(),
        finishedAt: new Date().toISOString(),
        rowCount: 0,
        executionTime: 0,
        error: '自然语言查询返回了无效的格式'
      };
    } catch (error) {
      console.error(`执行自然语言查询失败:`, error);
      // 返回错误结果
      return {
        id: 'error-' + Date.now(),
        columns: [],
        rows: [],
        sql: '',
        status: 'FAILED',
        createdAt: new Date().toISOString(),
        finishedAt: new Date().toISOString(),
        rowCount: 0,
        executionTime: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },

  /**
   * 取消查询执行
   */
  async cancelQuery(queryId: string): Promise<void> {
    try {
      console.log(`取消查询执行 [${queryId}]`);

      const apiUrl = getQueryApiUrl('cancel', { id: queryId });
      console.log('取消查询URL:', apiUrl);

      const response = await instance.post<any>(apiUrl);

      console.log('取消查询响应:', response);

      // 处理响应
      if (!response || response.status !== 200 || (response.data && response.data.success === false)) {
        throw new Error('取消查询失败');
      }
    } catch (error) {
      console.error(`取消查询执行失败 [${queryId}]:`, error);
      throw error;
    }
  },

  /**
   * 获取查询状态
   */
  async getQueryStatus(queryId: string): Promise<any> {
    try {
      console.log(`获取查询状态 [${queryId}]`);

      // 构造API路径并添加时间戳防止缓存
      const apiUrl = getQueryApiUrl('status', { id: queryId }) + `?_t=${Date.now()}`;
      console.log('获取查询状态URL:', apiUrl);

      const response = await instance.get<any>(apiUrl);

      console.log('查询状态响应:', response);

      // 处理响应数据
      if (response && response.data && response.data.success && response.data.data) {
        return response.data.data;
      }

      throw new Error('获取查询状态失败：无效的响应格式');
    } catch (error) {
      console.error(`获取查询状态失败 [${queryId}]:`, error);
      throw error;
    }
  },

  /**
   * 获取查询执行历史记录
   */
  async getQueryHistory(queryId: string, params: { page?: number, size?: number, status?: string, sort?: string } = {}): Promise<any[]> {
    const { page = 1, size = 10, status, sort } = params;

    // 构建正确的URL
    let url = getQueryApiUrl('history', { id: queryId });

    // 添加查询参数
    if (page) {
      url += `?page=${page}`;
    }

    if (size) {
      url += page ? `&size=${size}` : `?size=${size}`;
    }

    if (status) {
      url += `&status=${status}`;
    }

    if (sort) {
      url += `&sort=${sort}`;
    }

    // 添加时间戳防止缓存
    url += `&_t=${Date.now()}`;

    const response = await instance.get(url);

    if (!response || response.status !== 200) {
      throw new Error(`获取查询历史失败: ${response?.statusText || '未知错误'}`);
    }

    return response.data.data;
  },

  /**
   * 获取单个查询详情
   */
  async getQuery(id: string): Promise<Query> {
    try {
      console.log(`获取查询详情, ID: ${id}`);

      // 检查是否使用模拟数据
      const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

      // 在Mock模式下，直接返回模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据返回查询详情');

        // 从模拟数据中查找匹配的查询
        const mockQuery = mockQueries.find(q => q.id === id);

        if (!mockQuery) {
          console.warn(`未找到ID为${id}的模拟查询数据，返回第一条模拟数据`);
          // 如果没找到匹配的查询，返回第一条模拟数据并修改ID
          return {
            ...mockQueries[0],
            id: id
          };
        }

        return mockQuery;
      }

      // 以下是原有的真实API请求逻辑，使用getQueryApiUrl避免/api重复
      const apiUrl = getQueryApiUrl('detail', { id }) + `?_t=${Date.now()}`;
      console.log('获取查询详情URL:', apiUrl);

      try {
        // 捕获http请求的错误，以便更好地处理
        const response = await instance.get(apiUrl);

        // 注意：由于http拦截器，response可能是处理过的数据
        // 如果response包含data和success属性，说明拦截器已经返回了原始的API响应
        // 如果response包含status属性，说明拦截器返回了完整的axios响应

        console.log('查询详情原始响应:', response);

        // 情况1: 经过拦截器处理，直接返回了成功的data内容
        if (response && response.success === true && response.data) {
          console.log('拦截器处理过的成功响应 (情况1)');
          return response.data;
        }

        // 情况2: 拦截器未处理，返回了原始的axios响应
        else if (response && response.status && response.data) {
          console.log('原始axios响应 (情况2)');

          // 如果API响应有标准格式
          if (response.data.success === true && response.data.data) {
            return response.data.data;
          }
          // 如果API直接返回了查询对象
          else if (response.data.id === id) {
            return response.data;
          }
          // 无法识别的响应数据结构
          else {
            console.error('无法识别的响应数据结构:', response.data);
            throw new Error('获取查询详情失败: 响应格式错误');
          }
        }

        // 情况3: 直接返回了查询对象（没有包装在data中）
        else if (response && response.id === id) {
          console.log('直接返回了查询对象 (情况3)');
          return response;
        }

        // 无法识别的响应结构
        console.error('无法识别的响应结构:', response);
        throw new Error('获取查询详情失败: 无法解析响应数据');
      } catch (requestError) {
        // 处理请求过程中的错误
        console.error('请求过程中出错:', requestError);
        throw new Error(requestError instanceof Error ? requestError.message : '获取查询详情失败: 网络错误');
      }
    } catch (error) {
      console.error(`获取查询详情失败, ID: ${id}`, error);
      throw error;
    }
  },

  /**
   * 保存查询
   * @param query 查询对象
   * @returns 保存后的查询
   */
  async saveQuery(query: Partial<Query>): Promise<Query> {
    try {
      console.log('保存查询:', query);

      let apiUrl;
      if (query.id) {
        apiUrl = getQueryApiUrl('update', { id: query.id });
      } else {
        apiUrl = getQueryApiUrl('create');
      }
      console.log('保存查询URL:', apiUrl);

      let response;

      // 发送API请求
      if (query.id) {
        response = await instance.put(apiUrl, query);
      } else {
        response = await instance.post(apiUrl, query);
      }

      // 处理响应数据
      if (response && response.code === 200) {
        // 标准响应格式检查
        if (response.data && response.data.success === true && response.data.data) {
          return response.data.data;
        }
        // 直接返回对象的情况
        else if (response.data && (response.data.id || response.data.code === 200)) {
          return response.data;
        }
      }

      throw new Error('保存查询失败：无效的响应格式');
    } catch (error) {
      console.error('保存查询失败:', error);
      throw error;
    }
  },

  /**
   * 删除查询
   * @param id 查询ID
   * @returns 是否成功
   */
  async deleteQuery(id: string): Promise<void> {
    try {
      console.log(`删除查询 [${id}]`);

      const apiUrl = getQueryApiUrl('delete', { id });
      console.log('删除查询URL:', apiUrl);

      // 发送API请求
      const response = await instance.delete<any>(apiUrl);

      console.log('删除查询响应:', response);

      // 检查响应状态码
      if (!response || response.code !== 200) {
        throw new Error(`删除查询失败: ${response?.statusText || '未知错误'}`);
      }

      // 检查响应数据
      if (response.data && response.success === false) {
        throw new Error(response.message || '删除查询失败');
      }
    } catch (error) {
      console.error(`删除查询失败 [${id}]:`, error);
      throw error;
    }
  },

  /**
   * 获取收藏的查询列表
   * @param params 查询参数
   * @returns 收藏的查询列表
   */
  async getFavorites(params: QueryParams = {}): Promise<PageResponse<Query>> {
    return this.getFavoriteQueries(params);
  },

  /**
   * 获取查询参数
   */
  async getQueryParameters(id: string): Promise<QueryParameter[]> {
    try {
      console.log(`获取查询参数, 查询ID: ${id}`);

      // 检查是否使用模拟数据
      const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

      // 在Mock模式下，直接返回模拟数据
      if (USE_MOCK) {
        console.log(`使用模拟数据返回查询参数, 查询ID: ${id}`);

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 300));

        // 为查询生成模拟参数
        const mockParameters = Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, i) => {
          // 可能的参数类型
          const paramTypes = ['string', 'number', 'boolean', 'date'];
          const type = paramTypes[Math.floor(Math.random() * paramTypes.length)];

          // 生成参数对象
          const param: QueryParameter = {
            id: `param-${id}-${i+1}`,
            name: `param${i + 1}`,
            label: `参数 ${i + 1}`,
            type,
            required: Math.random() > 0.5,
            defaultValue: null,
            options: type === 'string' ? Array.from({ length: 3 }, (_, j) => ({ label: `选项${j+1}`, value: `value${j+1}` })) : undefined
          };

          // 根据类型生成默认值
          if (Math.random() > 0.3) {
            switch (type) {
              case 'string':
                param.defaultValue = `默认值${i + 1}`;
                break;
              case 'number':
                param.defaultValue = Math.floor(Math.random() * 100);
                break;
              case 'boolean':
                param.defaultValue = Math.random() > 0.5;
                break;
              case 'date':
                param.defaultValue = new Date().toISOString().split('T')[0];
                break;
            }
          }

          return param;
        });

        console.log(`返回${mockParameters.length}个模拟参数`);
        return mockParameters;
      }

      // 使用getQueryApiUrl函数获取正确的API路径
      const apiUrl = getQueryApiUrl('parameters', { id });
      console.log('获取查询参数URL:', apiUrl);

      // 使用instance.get发送请求
      const response = await instance.get<any>(apiUrl);

      console.log('查询参数响应原始数据:', response);

      // 提取响应数据
      const responseData = response.data;

      // 从响应中提取参数列表
      let parameters: QueryParameter[] = [];

      // 处理响应数据，增强参数提取逻辑
      if (response && response.data) {
        console.log('参数分析响应详情:', JSON.stringify(response.data).substring(0, 300));

        // 确保我们能处理不同层级的数据结构
        const responseData = response.data;

        // 情况1: 标准响应格式 {success: true, data: {parameters: []}}
        if (responseData.success === true && responseData.data) {
          if (responseData.data.parameters && Array.isArray(responseData.data.parameters)) {
            console.log('从responseData.data.parameters中提取参数');
            parameters = responseData.data.parameters;
          }
          // 情况2: {success: true, data: []}
          else if (Array.isArray(responseData.data)) {
            console.log('responseData.data本身是数组，直接作为参数列表');
            parameters = responseData.data;
          }
        }
        // 情况3: 直接返回参数数组
        else if (Array.isArray(responseData)) {
          console.log('responseData本身是数组，直接作为参数列表');
          parameters = responseData;
        }
        // 情况4: {parameters: []}
        else if (responseData.parameters && Array.isArray(responseData.parameters)) {
          console.log('从responseData.parameters中提取参数');
          parameters = responseData.parameters;
        }
        // 情况5: 响应中参数在顶层 {data: {parameters: []}}
        else if (responseData.data && responseData.data.parameters && Array.isArray(responseData.data.parameters)) {
          console.log('从responseData.data.parameters中提取参数');
          parameters = responseData.data.parameters;
        }
      }

      // 如果后端API没有返回参数，但本地解析有参数，则使用本地解析的结果
      if (parameters.length === 0 && localParams.length > 0) {
        console.log(`API未返回参数，使用本地解析的${localParams.length}个参数`);
        parameters = localParams.map(paramName => ({
          id: `param-${paramName}`,
          name: paramName,
          type: 'string',
          label: paramName,
          required: true
        }));
      }

      console.log(`成功分析出${parameters.length}个参数:`, parameters);
      return parameters;
    } catch (error) {
      console.error('获取查询参数失败:', error);
      throw error;
    }
  },

  /**
   * 分析SQL查询中的参数
   * @param params 包含数据源ID和SQL文本的参数对象
   * @returns 查询参数列表
   */
  async analyzeQueryParameters(params: {
    dataSourceId: string;
    sql: string;
  }): Promise<QueryParameter[]> {
    try {
      console.log('分析SQL参数, 参数:', params);

      // 导入提取SQL参数的工具函数
      const { extractSqlParameters } = await import('@/utils/sql-param-converter');

      // 使用本地正则表达式提取参数
      const localParams = extractSqlParameters(params.sql);

      // 使用Mock模式
      if (import.meta.env.VITE_USE_MOCK_API === 'true') {
        console.log('使用Mock数据分析SQL参数');

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 300));

        // 从本地解析的参数创建QueryParameter对象
        const mockParameters: QueryParameter[] = localParams.map(paramName => ({
          id: `param-${paramName}`,
          name: paramName,
          type: 'string',
          label: paramName,
          required: true
        }));

        console.log(`找到${mockParameters.length}个模拟参数:`, mockParameters);
        return mockParameters;
      }

      // 构造API路径
      const apiUrl = getQueryApiUrl('analyze-parameters');
      console.log('分析SQL参数URL:', apiUrl);

      // 构造请求参数 - 修改参数结构以匹配API要求
      const requestData = {
        sqlText: params.sql,
        dataSourceId: params.dataSourceId
      };

      // 发送API请求
      const response = await instance.post<any>(apiUrl, requestData);

      console.log('分析SQL参数响应:', response);

      // 从响应中提取参数列表
      let parameters: QueryParameter[] = [];

      // 处理响应数据，增强参数提取逻辑
      if (response && response.data) {
        console.log('参数分析响应详情:', JSON.stringify(response.data).substring(0, 300));

        // 确保我们能处理不同层级的数据结构
        const responseData = response.data;

        // 情况1: 标准响应格式 {success: true, data: {parameters: []}}
        if (responseData.success === true && responseData.data) {
          if (responseData.data.parameters && Array.isArray(responseData.data.parameters)) {
            console.log('从responseData.data.parameters中提取参数');
            parameters = responseData.data.parameters;
          }
          // 情况2: {success: true, data: []}
          else if (Array.isArray(responseData.data)) {
            console.log('responseData.data本身是数组，直接作为参数列表');
            parameters = responseData.data;
          }
        }
        // 情况3: 直接返回参数数组
        else if (Array.isArray(responseData)) {
          console.log('responseData本身是数组，直接作为参数列表');
          parameters = responseData;
        }
        // 情况4: {parameters: []}
        else if (responseData.parameters && Array.isArray(responseData.parameters)) {
          console.log('从responseData.parameters中提取参数');
          parameters = responseData.parameters;
        }
        // 情况5: 响应中参数在顶层 {data: {parameters: []}}
        else if (responseData.data && responseData.data.parameters && Array.isArray(responseData.data.parameters)) {
          console.log('从responseData.data.parameters中提取参数');
          parameters = responseData.data.parameters;
        }
      }

      // 如果后端API没有返回参数，但本地解析有参数，则使用本地解析的结果
      if (parameters.length === 0 && localParams.length > 0) {
        console.log(`API未返回参数，使用本地解析的${localParams.length}个参数`);
        parameters = localParams.map(paramName => ({
          id: `param-${paramName}`,
          name: paramName,
          type: 'string',
          label: paramName,
          required: true
        }));
      }

      console.log(`成功分析出${parameters.length}个参数:`, parameters);
      return parameters;
    } catch (error) {
      console.error('分析SQL参数失败:', error);

      // 出错时尝试本地解析参数
      try {
        const { extractSqlParameters } = await import('@/utils/sql-param-converter');
        const localParams = extractSqlParameters(params.sql);

        if (localParams.length > 0) {
          console.log(`API调用失败，使用本地解析的${localParams.length}个参数`);
          return localParams.map(paramName => ({
            id: `param-${paramName}`,
            name: paramName,
            type: 'string',
            label: paramName,
            required: true
          }));
        }
      } catch (innerError) {
        console.error('本地参数解析也失败:', innerError);
      }

      throw error;
    }
  },

  /**
   * 获取收藏的查询列表
   * @param params 查询参数
   * @returns 分页查询结果
   */
  async getFavoriteQueries(params: QueryParams = {}): Promise<PageResponse<Query>> {
    try {
      console.log('获取收藏查询列表, 参数:', params);

      // 标准化请求参数
      const queryParams = {
        page: params.page || 1,
        size: params.size || 10,
        searchTerm: params.searchTerm || params.search || undefined,
        queryType: params.queryType,
        sortBy: params.sortBy,
        sortDir: params.sortDir
      };

      // 使用后端实际API路径
      const apiUrl = getQueryApiUrl('favorites');
      const response = await instance.get<any>(apiUrl, { params: queryParams });

      console.log('收藏查询列表响应:', response);

      // 解析响应
      let items: Query[] = [];
      let pagination = {
        page: queryParams.page,
        size: queryParams.size,
        total: 0,
        totalPages: 0
      };

      // 处理标准响应格式
      if (response && response.success && response.data) {
        if (response.data.items) {
          items = response.data.items;
          pagination = {
            page: response.data.page || queryParams.page,
            size: response.data.size || queryParams.size,
            total: response.data.total || 0,
            totalPages: response.data.pages || 0
          };
        } else if (Array.isArray(response.data)) {
          items = response.data;
          pagination = {
            page: queryParams.page,
            size: queryParams.size,
            total: response.data.length,
            totalPages: Math.ceil(response.data.length / queryParams.size)
          };
        }
      }

      return {
        items,
        page: pagination.page,
        size: pagination.size,
        total: pagination.total,
        totalPages: pagination.totalPages
      };
    } catch (error) {
      console.error('获取收藏查询列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取查询执行历史记录
   * @param queryId 查询ID
   * @returns 查询执行历史记录列表
   */
  async getQueryExecutionHistory(queryId: string): Promise<any[]> {
    try {
      console.log(`获取查询执行历史，查询ID: ${queryId}`);

      // 检查是否使用模拟数据
      if (import.meta.env.VITE_USE_MOCK_API === 'true') {
        console.log('使用模拟数据返回查询执行历史');

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 创建一些模拟的执行历史记录
        const mockExecutions = Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, i) => {
          // 创建模拟的时间，最近的排在前面
          const executeTime = new Date();
          executeTime.setHours(executeTime.getHours() - i);

          // 随机生成状态
          const statuses = ['SUCCESS', 'ERROR', 'CANCELLED'];
          const status = statuses[Math.floor(Math.random() * statuses.length)];

          // 生成执行记录
          return {
            id: `exec-${queryId}-${Date.now() - i * 10000}`,
            queryId: queryId,
            executedAt: executeTime.toISOString(),
            duration: Math.floor(Math.random() * 5000) + 100, // 100ms - 5100ms
            status: status,
            rowCount: status === 'SUCCESS' ? Math.floor(Math.random() * 1000) + 1 : undefined,
            errorMessage: status === 'ERROR' ? '查询执行异常: SQL语法错误' : undefined
          };
        });

        return mockExecutions;
      }

      // 构建API路径
      const apiUrl = getQueryApiUrl('execution-history', { id: queryId });
      console.log('获取查询执行历史URL:', apiUrl);

      // 使用instance.get发送请求
      const response = await instance.get<any>(apiUrl);

      console.log('查询执行历史响应:', response);

      // 从响应中提取执行历史列表
      let executions: any[] = [];

      // 处理响应数据
      if (response && response.data) {
        // 情况1: 标准响应格式 {success: true, data: {items: []}}
        if (response.data.success === true && response.data.data) {
          if (response.data.data.items && Array.isArray(response.data.data.items)) {
            executions = response.data.data.items;
          }
          // 情况2: {success: true, data: []}
          else if (Array.isArray(response.data.data)) {
            executions = response.data.data;
          }
        }
        // 情况3: 响应本身是标准格式 {success: true, data: []}
        else if (response.success === true && response.data) {
          if (Array.isArray(response.data)) {
            executions = response.data;
          }
          // 如果data是对象但包含items字段
          else if (response.data.items && Array.isArray(response.data.items)) {
            executions = response.data.items;
          }
        }
        // 情况4: 响应本身是数组
        else if (Array.isArray(response.data)) {
          executions = response.data;
        }
        else {
          console.warn('无法解析执行历史响应:', response.data);
        }
      }

      return executions;
    } catch (error) {
      console.error('获取查询执行历史失败:', error);
      throw error;
    }
  },
}

// 导出服务
export { queryService };

// 保留默认导出以兼容已有代码
export default queryService;
