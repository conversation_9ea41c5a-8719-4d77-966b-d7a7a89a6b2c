<template>
  <!-- 添加调试信息，仅开发模式显示 -->
  <div v-if="false" style="display: none;">
    显示状态: {{ visible ? '显示' : '隐藏' }}
    进度: {{ progress }}
  </div>
  
  <!-- 修复临时调试代码，恢复正确的v-if条件，并增加对visible的强制布尔转换 -->
  <div v-if="!!visible" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50" style="z-index: 9999;">
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] flex flex-col">
      <!-- 标题栏 -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">发布到低代码平台</h3>
        <button 
          @click="close" 
          class="text-gray-400 hover:text-gray-500 focus:outline-none"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <!-- 内容区域 -->
      <div class="px-6 py-4 flex-grow overflow-y-auto">
        <!-- 进度条 -->
        <div class="mb-4">
          <div class="flex justify-between items-center mb-1">
            <span class="text-sm font-medium text-gray-700">发布进度</span>
            <span class="text-sm font-medium text-gray-700">{{ progressText }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <p class="mt-2 text-sm text-gray-500">{{ statusMessage }}</p>
        </div>
        
        <!-- 状态信息 -->
        <div class="mb-4">
          <div v-if="isProcessing" class="flex items-center justify-center">
            <i class="fas fa-spinner fa-spin text-blue-500 text-xl mr-2"></i>
            <span>{{ currentStepMessage }}</span>
          </div>
          
          <div v-if="error" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div class="flex">
              <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-2"></i>
              <div>
                <p class="text-sm font-medium text-red-800">发布过程中出现错误</p>
                <p class="mt-1 text-sm text-red-700">{{ error }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="result && result.success" class="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <div class="flex">
              <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2"></i>
              <div>
                <p class="text-sm font-medium text-green-800">发布成功</p>
                <p class="mt-1 text-sm text-green-700">低代码页面Code: integration_{{ result.id ? result.id.replace(/-/g, '_') : '' }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 如果发布成功，显示预览链接 -->
        <div v-if="result && result.success" class="mt-4">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">预览链接</label>
            <div class="flex gap-2">
              <input 
                ref="previewLinkInput"
                type="text" 
                readonly 
                :value="previewLink" 
                class="flex-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md bg-gray-50"
              />
              <button 
                @click="copyPreviewLink" 
                class="px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
              >
                <i :class="[copied ? 'fas fa-check' : 'fas fa-copy', 'mr-1']"></i>
                {{ copied ? '已复制' : '复制' }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end">
        <button 
          v-if="!isProcessing"
          @click="close" 
          class="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3 focus:outline-none"
        >
          关闭
        </button>
        <button 
          v-if="result && result.success"
          @click="openPreview" 
          class="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
        >
          打开预览
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useIntegrationStore } from '@/stores/integration';

// 获取集成store
const integrationStore = useIntegrationStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: 0
  },
  currentStep: {
    type: String,
    default: 'preparing'  // preparing, converting, publishing, completed
  },
  error: {
    type: String,
    default: ''
  },
  result: {
    type: Object as () => { id: string, success: boolean, message: string } | null,
    default: null
  }
});

const emit = defineEmits(['close', 'open-preview']);

// 本地状态
const copied = ref(false);
const previewLinkInput = ref<HTMLInputElement | null>(null);

// 计算属性
const isProcessing = computed(() => {
  return props.currentStep !== 'completed' && !props.error;
});

const progressText = computed(() => {
  return `${props.progress}%`;
});

const statusMessage = computed(() => {
  if (props.error) {
    return '发布失败';
  }
  
  if (props.result && props.result.success) {
    return '发布成功';
  }
  
  return '正在处理...';
});

const currentStepMessage = computed(() => {
  switch (props.currentStep) {
    case 'preparing':
      return '准备发布数据...';
    case 'converting':
      return '转换为低代码格式...';
    case 'publishing':
      return '发布到低代码平台...';
    case 'completed':
      return '发布完成';
    default:
      return '处理中...';
  }
});

const previewLink = computed(() => {
  if (props.result && props.result.id) {
    // 根据环境动态生成预览链接域名
    const domain = import.meta.env.PROD ? 'boss.yeepay.com' : 'qaboss.yeepay.com';
    return `https://${domain}/low-code-editor/2_18_3/preview/${props.result.id}`;
  }
  return '';
});

// 方法
const close = () => {
  emit('close');
};

const openPreview = () => {
  if (previewLink.value) {
    console.log('[PublishLowcodeModal] 点击打开预览按钮，预览链接:', previewLink.value);
    // 调用父组件emit的事件处理方法，将预览URL传递过去
    emit('open-preview', previewLink.value);
  } else {
    console.error('[PublishLowcodeModal] 预览链接为空，无法打开预览');
    copied.value = false;
  }
};

const copyPreviewLink = () => {
  if (previewLinkInput.value) {
    previewLinkInput.value.select();
    document.execCommand('copy');
    copied.value = true;
    
    setTimeout(() => {
      copied.value = false;
    }, 2000);
  }
};

// 添加watch监控visible属性变化
watch(() => props.visible, (newValue) => {
  console.log('[PublishLowcodeModal] 弹窗显示状态变化:', newValue);
  if (newValue) {
    console.log('[PublishLowcodeModal] 显示弹窗，当前进度:', props.progress, '当前步骤:', props.currentStep);
    copied.value = false;
  }
});

// 监控progress变化
watch(() => props.progress, (newValue) => {
  console.log('[PublishLowcodeModal] 进度更新:', newValue);
});

// 监控currentStep变化
watch(() => props.currentStep, (newValue) => {
  console.log('[PublishLowcodeModal] 步骤更新:', newValue);
});

// 监控error变化
watch(() => props.error, (newValue) => {
  if (newValue) {
    console.log('[PublishLowcodeModal] 发生错误:', newValue);
  }
});

// 监控result变化
watch(() => props.result, (newValue) => {
  if (newValue) {
    console.log('[PublishLowcodeModal] 收到结果:', newValue);
    
    // 如果发布成功，存储预览URL
    if (newValue.success && newValue.id) {
      // 根据环境动态生成预览链接域名
      const domain = import.meta.env.PROD ? 'boss.yeepay.com' : 'qaboss.yeepay.com';
      const url = `https://${domain}/low-code-editor/2_18_3/preview/${newValue.id}`;
      integrationStore.lastPublishedPreviewUrl = url;
      console.log('[PublishLowcodeModal] 已更新预览URL到store:', url);
    }
  }
});

// 在组件挂载时添加检查
onMounted(() => {
  console.log('[PublishLowcodeModal] 组件已挂载，visible状态:', props.visible);
  
  // 检查父组件是否正确绑定了visible属性
  if (props.visible === undefined) {
    console.warn('[PublishLowcodeModal] 警告: visible属性未绑定或为undefined');
  }
  
  // 检查是否存在样式问题（例如被父元素遮挡）
  if (props.visible) {
    console.log('[PublishLowcodeModal] 组件应该显示，检查DOM元素是否可见');
    setTimeout(() => {
      const modalElement = document.querySelector('.fixed.inset-0.bg-gray-600');
      if (modalElement) {
        console.log('[PublishLowcodeModal] 找到模态框DOM元素');
        const styles = window.getComputedStyle(modalElement);
        console.log('[PublishLowcodeModal] 模态框计算样式:', {
          display: styles.display,
          visibility: styles.visibility,
          opacity: styles.opacity,
          zIndex: styles.zIndex
        });
      } else {
        console.warn('[PublishLowcodeModal] 无法找到模态框DOM元素，可能未正确渲染');
      }
    }, 100);
  }
});
</script>