# 低代码预览页面分页功能修复

## 问题描述

在低代码生成的预览页面中，点击分页按钮时没有调用后端接口，导致分页功能无效。

**问题URL示例：**
```
https://qaboss.yeepay.com/data-scope/api/queries/3e3e30e7bf9846a3aa3bd745176ad092/versions/135c2ffaabd04457b0c2eada10687d0e/int-447a2cf257a141f59452a871fa5ac911/execute-append?pageCode=int-447a2cf257a141f59452a871fa5ac911&page=2&size=20&order=&REQ_ID=&PROD=&CRD_NO=&CRD_TYP=&AMT=&MOBILE=&USR_ID=&MEC_NO=&USR_IP=&OCC_TM=,&BANK_ORDERNO=&FRP_CODE=&ID_NO=&TERMINAL_ID=&AGENCY_NO=&TARGET_MERCHANT_NO=
```

## 修复内容

### 1. 修改文件：`src/views/integration/IntegrationPreview.vue`

#### 主要修改：

1. **新增通用数据加载函数 `loadDataFromApi`**：
   - 构建正确的 `execute-append` API URL
   - 处理分页参数（page、size等）
   - 添加表单参数到查询字符串
   - 包含错误处理和降级到模拟数据

2. **修改 `handlePageChange` 函数**：
   - 从简单的日志打印改为实际调用API
   - 使用新的通用数据加载函数
   - 添加加载状态管理

3. **新增 `handlePageSizeChange` 函数**：
   - 处理每页显示数量变化
   - 更新分页配置
   - 重新加载第一页数据

4. **修改 `loadTableData` 函数**：
   - 使用新的通用数据加载函数
   - 保持向后兼容性（API失败时使用模拟数据）

5. **更新 `TableView` 组件事件绑定**：
   - 添加 `@page-change="handlePageChange"` 事件监听
   - 添加 `@page-size-change="handlePageSizeChange"` 事件监听
   - 添加 `:loading="tableLoading"` 属性绑定

## 技术实现细节

### API URL 构建逻辑

```javascript
// 根据是否有版本ID构建不同的API路径
let apiPath;
if (versionId) {
  apiPath = `/data-scope/api/queries/${queryId}/versions/${versionId}/${integrationId}/execute-append`;
} else {
  apiPath = `/data-scope/api/queries/${queryId}/${integrationId}/execute-append`;
}
```

### 查询参数处理

```javascript
const queryParams = new URLSearchParams({
  pageCode: integrationId,
  page: page.toString(),
  size: (tablePagination.value.pageSize || 10).toString(),
  // ... 其他固定参数
});

// 添加表单参数
if (formValues.value) {
  Object.entries(formValues.value).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.set(key, String(value));
    }
  });
}
```

### 错误处理和降级策略

- API调用失败时自动降级到模拟数据
- 缺少必要参数时使用模拟数据
- 保持用户体验的连续性

## 测试方法

### 1. 功能测试

#### 分页测试：
1. 打开低代码预览页面
2. 点击分页按钮（第2页、第3页等）
3. 检查浏览器开发者工具的Network标签
4. 确认发送了正确的API请求

#### 页面大小变化测试：
1. 在分页器中选择不同的每页显示数量（10、20、50等）
2. 检查是否发送了新的API请求
3. 确认页码重置为第1页
4. 确认size参数正确传递

### 2. 预期行为

- 点击分页时应该看到网络请求
- URL应该包含正确的分页参数
- 表格数据应该更新（即使是模拟数据）
- 加载状态应该正确显示

### 3. 调试信息

在浏览器控制台中可以看到以下日志：
```
页码变化: 2
数据查询URL: /data-scope/api/queries/.../execute-append?pageCode=...&page=2&size=20...
分页数据更新成功，共 XX 条记录
```

## 兼容性说明

- 保持向后兼容：API失败时自动使用模拟数据
- 不影响现有功能：其他组件和功能保持不变
- 渐进式增强：真实API可用时使用真实数据，否则使用模拟数据

## 注意事项

1. 需要确保后端API接口正常工作
2. 需要正确的认证token
3. 集成配置中需要包含正确的queryId和integrationId
4. 如果API返回格式与预期不符，会自动降级到模拟数据
